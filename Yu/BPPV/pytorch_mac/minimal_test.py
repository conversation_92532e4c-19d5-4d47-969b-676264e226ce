#!/usr/bin/env python3
"""
Minimal test for BPPV four-class classification - only 1-2 samples for CPU testing.
"""

import torch
import sys
sys.path.append('.')

import config
from data import BPPVFourClassDataset

def main():
    print("MINIMAL TEST - BPPV Four-Class Classification (CPU-friendly)")
    print("=" * 60)
    
    # Test with very small parameters for CPU
    print("Creating dataset with minimal parameters...")
    try:
        dataset = BPPVFourClassDataset(
            class_files=config.CLASS_FILES,
            data_root=config.DATA_ROOT,
            max_frames=5,      # Very small frame count
            resize_factor=0.5, # Less aggressive resize
            convert_to_grayscale=True
        )
        print(f"✓ Dataset created: {len(dataset)} total samples")
        
        # Test only first sample
        print(f"\nTesting sample 0...")
        sample = dataset[0]
        video_list, label = sample
        
        print(f"✓ Sample loaded successfully")
        print(f"✓ Videos: {len(video_list)}")
        print(f"✓ Label: {label} ({config.CLASS_NAMES[label]})")
        
        for i, video in enumerate(video_list):
            print(f"✓ Video {i}: {video.shape} ({video.dtype})")
        
        # Simple model test
        print(f"\nTesting simple model...")
        
        class MinimalModel(torch.nn.Module):
            def __init__(self):
                super().__init__()
                self.fc = torch.nn.Linear(4, 4)  # 4 videos -> 4 classes
                
            def forward(self, video_list):
                # Ultra-simple: just use the mean of each video
                features = []
                for video in video_list:
                    # Take mean across all dimensions except batch
                    mean_val = video.mean()
                    features.append(mean_val.unsqueeze(0))  # Add batch dim
                
                # Stack to get (batch=1, 4)
                combined = torch.stack(features, dim=0).unsqueeze(0)
                return self.fc(combined)
        
        model = MinimalModel()
        
        # Test forward pass
        with torch.no_grad():
            output = model(video_list)
            predicted_class = torch.argmax(output, dim=1).item()
            
        print(f"✓ Model forward pass successful")
        print(f"✓ Output shape: {output.shape}")
        print(f"✓ Predicted class: {predicted_class} ({config.CLASS_NAMES[predicted_class]})")
        print(f"✓ True class: {label} ({config.CLASS_NAMES[label]})")
        
        # Test training step
        print(f"\nTesting training step...")
        criterion = torch.nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        model.train()
        optimizer.zero_grad()
        
        output = model(video_list)
        loss = criterion(output, torch.tensor([label]))
        loss.backward()
        optimizer.step()
        
        print(f"✓ Training step completed")
        print(f"✓ Loss: {loss.item():.4f}")
        
        print("\n" + "=" * 60)
        print("🎉 MINIMAL TEST PASSED!")
        print("\n核心功能验证成功：")
        print("✓ 数据加载：4个分类，每个样本4个视频")
        print("✓ 视频处理：转灰度，缩放分辨率")
        print("✓ 模型推理：四分类输出")
        print("✓ 训练步骤：损失计算和反向传播")
        
        print(f"\n数据统计：")
        print(f"- 总样本数：{len(dataset)}")
        print(f"- 视频形状：{video_list[0].shape}")
        print(f"- 类别编码：{dict(enumerate(config.CLASS_NAMES))}")
        
        print(f"\n使用方法：")
        print("python main.py --mode four_class --concatenate_mode feature_concat")
        print("python main.py --mode four_class --concatenate_mode separate_fusion")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
