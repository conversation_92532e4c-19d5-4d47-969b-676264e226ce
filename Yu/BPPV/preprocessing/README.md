# BPPV数据预处理程序

## 概述

这是一个用于处理BPPV（良性阵发性位置性眩晕）数据的预处理程序，能够扫描视频文件、生成路径记录、处理视频（裁剪上半部分）并验证结果。

## 功能特性

### 🔍 核心功能
- **目录扫描**：遍历BPPV数据目录，识别4个主要类别
- **路径记录**：生成两列格式的txt文件（视频类型 + 相对路径）
- **视频处理**：自动裁剪视频保留上半部分（高度减半）
- **路径验证**：验证原始和处理后文件的有效性

### 📁 支持的数据结构
```
BPPV/
├── horizontal_left/     (左水平半规管管石症)
├── horizontal_right/    (右水平半规管管石症)  
├── posterior_left/      (左后半规管管石症)
└── posterior_right/     (右后半规管管石症)
```

每个类别下包含患者文件夹，每个患者文件夹下有4个子目录：
- `DHL/` - 包含DHL-1.mp4等文件
- `DHR/` - 包含DHR-1.mp4等文件  
- `ROL/` - 包含ROL-1.mp4等文件
- `ROR/` - 包含ROR-1.mp4等文件

## 安装依赖

```bash
pip install opencv-python numpy
```

## 使用方法

### 基本用法

```bash
# 完整处理（扫描 + 视频裁剪 + 验证）
python3 bppv_preprocessor.py --root /path/to/BPPV/data

# 仅生成路径文件，跳过视频处理
python3 bppv_preprocessor.py --root /path/to/BPPV/data --skip-processing
```

### 示例

```bash
# 处理示例数据
python3 preprocessing/bppv_preprocessor.py --root /Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/BPPV/data/demo2025_07_13/BPPV

# 仅扫描和生成txt文件
python3 preprocessing/bppv_preprocessor.py --root /Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/BPPV/data/demo2025_07_13/BPPV --skip-processing
```

## 输出结果

### 📄 txt文件
程序会在 `BPPV_split` 目录下生成4个txt文件：
- `horizontal_left.txt`
- `horizontal_right.txt`
- `posterior_left.txt`
- `posterior_right.txt`

**文件格式**（制表符分隔）：
```
DHL    horizontal_left/患者姓名/DHL/DHL-1.mp4
DHR    horizontal_left/患者姓名/DHR/DHR-1.mp4
ROL    horizontal_left/患者姓名/ROL/ROL-1.mp4
ROR    horizontal_left/患者姓名/ROR/ROR-1.mp4
```

### 🎬 处理后的视频
视频文件保存在 `BPPV_split` 目录下，保持原有的目录结构：
```
BPPV_split/
├── horizontal_left.txt
├── horizontal_right.txt
├── posterior_left.txt
├── posterior_right.txt
└── horizontal_left/
    └── 患者姓名/
        ├── DHL/DHL-1.mp4  (处理后的视频)
        ├── DHR/DHR-1.mp4
        ├── ROL/ROL-1.mp4
        └── ROR/ROR-1.mp4
```

## 视频处理详情

### 🎯 处理规格
- **输入格式**：mp4视频文件
- **处理方式**：裁剪保留上半部分（高度减半）
- **示例转换**：1440×1080 → 1440×540
- **保持属性**：帧率、总帧数保持不变

### ✅ 质量验证
程序会自动验证：
- 原始文件路径有效性
- 处理后视频文件正确性
- 视频尺寸是否符合预期（高度减半）

## 程序流程

1. **扫描阶段**：遍历BPPV目录，发现所有视频文件
2. **记录阶段**：生成原始路径的txt文件
3. **验证阶段**：验证原始文件路径有效性
4. **处理阶段**：创建输出目录结构，批量处理视频
5. **更新阶段**：更新txt文件路径指向处理后的视频
6. **验证阶段**：验证处理后的视频文件

## 命令行参数

- `--root`：**必需**，指定BPPV数据根目录路径
- `--skip-processing`：**可选**，跳过视频处理，仅生成路径文件

## 错误处理

程序包含完善的错误处理机制：
- 文件不存在检测
- 视频处理失败报告
- 路径验证失败提示
- 详细的进度显示

## 性能说明

- **处理速度**：取决于视频文件大小和数量
- **存储需求**：处理后的视频约为原始大小的50%
- **内存使用**：逐帧处理，内存占用较低

## 注意事项

1. **存储空间**：确保有足够空间存储处理后的视频
2. **文件权限**：确保对输入和输出目录有读写权限
3. **中断恢复**：程序可以安全中断，重新运行会覆盖已有文件
4. **路径格式**：txt文件中的路径相对于BPPV_split目录

## 开发历史

### 版本更新记录

**v1.0** - 初始版本
- 基本的目录扫描和txt文件生成功能
- 命令行参数支持

**v1.1** - 视频处理功能
- 添加OpenCV视频裁剪功能
- 批量处理支持
- 进度显示

**v1.2** - 路径修复
- 修复txt文件保存位置（现在保存在BPPV_split目录下）
- 修复txt文件中路径格式（相对于BPPV_split目录）
- 完善验证功能

## 技术实现

- **语言**：Python 3
- **主要依赖**：OpenCV, pathlib, argparse
- **架构**：面向对象设计，单一职责原则
- **错误处理**：异常捕获和用户友好的错误信息

## 详细修改记录

### 🔧 主要问题修复

#### 问题1：txt文件保存位置错误
**问题描述**：txt文件生成在程序运行目录而不是BPPV_split目录
**解决方案**：
```python
# 修改前
with open(txt_filename, 'w', encoding='utf-8') as f:

# 修改后
output_dir = self.output_path
output_dir.mkdir(parents=True, exist_ok=True)
txt_path = output_dir / txt_filename
with open(txt_path, 'w', encoding='utf-8') as f:
```

#### 问题2：txt文件路径格式错误
**问题描述**：处理后的txt文件包含错误的路径前缀
**解决方案**：
```python
# 修改前
output_relative_path = f"{self.output_path.name}/{relative_path}"
f.write(f"{video_type}\t{output_relative_path}\n")

# 修改后
f.write(f"{video_type}\t{relative_path}\n")  # 直接使用相对路径
```

#### 问题3：视频处理路径构建错误
**问题描述**：输出路径构建逻辑有误，导致视频无法正确保存
**解决方案**：
```python
# 修改前
output_relative_path = relative_path.replace(self.root_path.name, self.output_path.name)
output_path = self.output_path.parent / output_relative_path

# 修改后
output_path = self.output_path / relative_path
```

### 🧪 测试验证

#### 测试用例1：路径扫描
- ✅ 成功扫描80个视频文件
- ✅ 正确识别4个类别
- ✅ 路径验证通过

#### 测试用例2：视频处理
- ✅ 成功处理测试视频（1440×1080 → 1440×540）
- ✅ 保持帧率和总帧数不变
- ✅ 输出文件大小约为原始的50%

#### 测试用例3：txt文件生成
- ✅ txt文件正确保存在BPPV_split目录
- ✅ 路径格式正确（相对于BPPV_split目录）
- ✅ 两列格式正确（视频类型 + 路径）

### 📋 最终验证结果

**目录结构验证**：
```
BPPV_split/
├── horizontal_left.txt          ✅ 位置正确
├── horizontal_right.txt         ✅ 位置正确
├── posterior_left.txt           ✅ 位置正确
├── posterior_right.txt          ✅ 位置正确
└── horizontal_left/             ✅ 视频目录正确
    └── 2585姚轩明（左水平半规管管石症）/
        ├── DHL/DHL-1.mp4       ✅ 视频文件存在
        ├── DHR/DHR-1.mp4       ✅ 视频文件存在
        ├── ROL/ROL-1.mp4       ✅ 视频文件存在
        └── ROR/ROR-1.mp4       ✅ 视频文件存在
```

**txt文件内容验证**：
```
DHL    horizontal_left/2585姚轩明（左水平半规管管石症）/DHL/DHL-1.mp4  ✅ 格式正确
DHR    horizontal_left/2585姚轩明（左水平半规管管石症）/DHR/DHR-1.mp4  ✅ 格式正确
ROL    horizontal_left/2585姚轩明（左水平半规管管石症）/ROL/ROL-1.mp4  ✅ 格式正确
ROR    horizontal_left/2585姚轩明（左水平半规管管石症）/ROR/ROR-1.mp4  ✅ 格式正确
```

## 联系信息

如有问题或建议，请联系开发团队。
