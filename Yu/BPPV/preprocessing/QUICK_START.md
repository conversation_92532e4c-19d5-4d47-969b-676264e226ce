# BPPV预处理程序 - 快速开始指南

## 🚀 5分钟快速上手

### 1. 检查依赖
```bash
# 确保已安装Python 3和pip
python3 --version
pip3 --version

# 安装必要依赖
pip3 install opencv-python numpy
```

### 2. 准备数据
确保你的BPPV数据目录结构如下：
```
BPPV/
├── horizontal_left/
│   └── 患者文件夹/
│       ├── DHL/DHL-1.mp4
│       ├── DHR/DHR-1.mp4
│       ├── ROL/ROL-1.mp4
│       └── ROR/ROR-1.mp4
├── horizontal_right/
├── posterior_left/
└── posterior_right/
```

### 3. 运行程序

#### 选项A：完整处理（推荐）
```bash
cd /Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/BPPV/preprocessing

python3 bppv_preprocessor.py --root /path/to/your/BPPV/data
```

#### 选项B：仅生成txt文件
```bash
python3 bppv_preprocessor.py --root /path/to/your/BPPV/data --skip-processing
```

### 4. 检查结果
程序完成后，检查输出目录：
```bash
ls -la /path/to/your/BPPV/data/../BPPV_split/
```

你应该看到：
- ✅ 4个txt文件：`horizontal_left.txt`, `horizontal_right.txt`, `posterior_left.txt`, `posterior_right.txt`
- ✅ 视频目录：包含处理后的视频文件

## 📋 示例命令

### 使用示例数据
```bash
# 进入程序目录
cd /Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/BPPV/preprocessing

# 处理示例数据
python3 bppv_preprocessor.py --root /Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/BPPV/data/demo2025_07_13/BPPV

# 仅生成txt文件（快速测试）
python3 bppv_preprocessor.py --root /Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/BPPV/data/demo2025_07_13/BPPV --skip-processing
```

## 🔍 输出说明

### txt文件格式
每个txt文件包含两列（制表符分隔）：
```
视频类型    相对路径
DHL        horizontal_left/患者姓名/DHL/DHL-1.mp4
DHR        horizontal_left/患者姓名/DHR/DHR-1.mp4
```

### 视频处理
- **原始**：1440×1080（示例）
- **处理后**：1440×540（保留上半部分）
- **文件大小**：约为原始的50%

## ⚠️ 常见问题

### Q1: 提示"ModuleNotFoundError: No module named 'cv2'"
**解决**：安装OpenCV
```bash
pip3 install opencv-python
```

### Q2: 提示"错误: 根目录不存在"
**解决**：检查路径是否正确
```bash
ls -la /path/to/your/BPPV/data
```

### Q3: 程序运行很慢
**原因**：视频处理需要时间，这是正常的
**建议**：
- 先用`--skip-processing`测试
- 确保有足够的存储空间
- 可以随时Ctrl+C中断

### Q4: txt文件为空或内容不对
**检查**：
1. 数据目录结构是否正确
2. 视频文件是否存在
3. 文件权限是否正确

## 📊 性能参考

| 视频数量 | 处理时间（估算） | 存储需求 |
|---------|----------------|----------|
| 10个视频 | 2-5分钟 | 原始大小的50% |
| 50个视频 | 10-20分钟 | 原始大小的50% |
| 100个视频 | 20-40分钟 | 原始大小的50% |

*实际时间取决于视频大小和硬件性能*

## 🛠️ 高级用法

### 批量处理多个数据集
```bash
# 创建批处理脚本
for dataset in dataset1 dataset2 dataset3; do
    python3 bppv_preprocessor.py --root /path/to/$dataset/BPPV
done
```

### 验证处理结果
```bash
# 检查视频文件数量
find /path/to/BPPV_split -name "*.mp4" | wc -l

# 检查txt文件内容
head -5 /path/to/BPPV_split/horizontal_left.txt
```

## 📞 获取帮助

### 查看帮助信息
```bash
python3 bppv_preprocessor.py --help
```

### 程序输出示例
```
BPPV数据预处理程序
输入目录: /path/to/BPPV
输出目录: /path/to/BPPV_split
--------------------------------------------------
正在扫描视频文件...
扫描完成: 发现 80 个视频文件
正在生成txt文件...
生成文件: /path/to/BPPV_split/horizontal_left.txt (20 个视频)
...
正在验证文件路径...
验证完成: 所有 80 个文件都存在
--------------------------------------------------
预处理完成!
```

## 🎯 下一步

1. **验证结果**：检查生成的txt文件和视频文件
2. **备份数据**：建议备份原始数据
3. **集成使用**：将txt文件用于后续的机器学习或分析任务

---

**需要更多帮助？** 查看 `README.md` 获取详细文档，或查看 `CHANGELOG.md` 了解最新更新。
