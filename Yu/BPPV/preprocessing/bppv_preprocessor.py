#!/usr/bin/env python3
"""
BPPV数据预处理程序
功能：
1. 遍历BPPV数据目录，生成文件路径记录
2. 验证文件路径有效性
3. 对视频进行裁剪处理（640x480 -> 640x240，保留上半部分）
4. 更新txt文件路径指向处理后的视频

使用方式：
python bppv_preprocessor.py --root /path/to/BPPV/data
"""

import os
import argparse
import cv2
import shutil
from pathlib import Path
from typing import List, Tuple, Dict
import sys


class BPPVPreprocessor:
    def __init__(self, root_path: str):
        """
        初始化BPPV预处理器
        
        Args:
            root_path: BPPV数据根目录路径
        """
        self.root_path = Path(root_path)
        self.output_path = self.root_path.parent / (self.root_path.name + "_split")
        self.categories = ["horizontal_left", "horizontal_right", "posterior_left", "posterior_right"]
        self.video_types = ["DHL", "DHR", "ROL", "ROR"]
        
    def scan_videos(self) -> Dict[str, List[Tuple[str, str]]]:
        """
        扫描所有视频文件并按类别分组
        
        Returns:
            字典，键为类别名，值为(视频类型, 相对路径)的列表
        """
        print("正在扫描视频文件...")
        video_data = {category: [] for category in self.categories}
        
        for category in self.categories:
            category_path = self.root_path / category
            if not category_path.exists():
                print(f"警告: 类别目录不存在: {category_path}")
                continue
                
            # 遍历患者文件夹
            for patient_dir in category_path.iterdir():
                if not patient_dir.is_dir():
                    continue
                    
                # 遍历视频类型文件夹
                for video_type in self.video_types:
                    video_type_path = patient_dir / video_type
                    if not video_type_path.exists():
                        continue
                        
                    # 查找mp4文件
                    for video_file in video_type_path.glob("*.mp4"):
                        relative_path = video_file.relative_to(self.root_path)
                        video_data[category].append((video_type, str(relative_path)))
                        
        return video_data
    
    def generate_txt_files(self, video_data: Dict[str, List[Tuple[str, str]]], use_split_path: bool = False):
        """
        生成txt文件记录视频路径

        Args:
            video_data: 视频数据字典
            use_split_path: 是否使用split路径（处理后的视频路径）
        """
        print("正在生成txt文件...")

        # 确保输出目录存在
        if use_split_path:
            output_dir = self.output_path
        else:
            output_dir = self.output_path
        output_dir.mkdir(parents=True, exist_ok=True)

        for category, videos in video_data.items():
            txt_filename = f"{category}.txt"
            txt_path = output_dir / txt_filename

            with open(txt_path, 'w', encoding='utf-8') as f:
                for video_type, relative_path in videos:
                    if use_split_path:
                        # 使用相对于BPPV_split目录的路径
                        f.write(f"{video_type}\t{relative_path}\n")
                    else:
                        f.write(f"{video_type}\t{relative_path}\n")
                    
            print(f"生成文件: {txt_path} ({len(videos)} 个视频)")
    
    def verify_paths(self, video_data: Dict[str, List[Tuple[str, str]]], use_split_path: bool = False) -> bool:
        """
        验证文件路径是否存在
        
        Args:
            video_data: 视频数据字典
            use_split_path: 是否验证split路径
            
        Returns:
            所有文件是否都存在
        """
        print("正在验证文件路径...")
        
        base_path = self.output_path if use_split_path else self.root_path
        missing_files = []
        total_files = 0
        
        for category, videos in video_data.items():
            for video_type, relative_path in videos:
                total_files += 1
                if use_split_path:
                    full_path = self.output_path / relative_path
                else:
                    full_path = base_path / relative_path
                if not full_path.exists():
                    missing_files.append(str(full_path))
        
        if missing_files:
            print(f"发现 {len(missing_files)} 个缺失文件:")
            for missing_file in missing_files[:10]:  # 只显示前10个
                print(f"  - {missing_file}")
            if len(missing_files) > 10:
                print(f"  ... 还有 {len(missing_files) - 10} 个文件")
            return False
        else:
            print(f"验证完成: 所有 {total_files} 个文件都存在")
            return True
    
    def create_output_structure(self, video_data: Dict[str, List[Tuple[str, str]]]):
        """
        创建输出目录结构
        """
        print(f"正在创建输出目录结构: {self.output_path}")
        
        # 创建主目录
        self.output_path.mkdir(exist_ok=True)
        
        # 创建所有需要的子目录
        created_dirs = set()
        for category, videos in video_data.items():
            for video_type, relative_path in videos:
                # 构建输出路径
                output_full_path = self.output_path / relative_path
                output_dir = output_full_path.parent
                
                if str(output_dir) not in created_dirs:
                    output_dir.mkdir(parents=True, exist_ok=True)
                    created_dirs.add(str(output_dir))
        
        print(f"创建了 {len(created_dirs)} 个目录")

    def crop_video(self, input_path: Path, output_path: Path) -> bool:
        """
        裁剪视频，保留上半部分（高度减半）

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径

        Returns:
            是否成功处理
        """
        try:
            # 打开输入视频
            cap = cv2.VideoCapture(str(input_path))
            if not cap.isOpened():
                print(f"错误: 无法打开视频文件 {input_path}")
                return False

            # 获取视频属性
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # 设置输出视频参数
            crop_height = height // 2  # 保留上半部分
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, crop_height))

            if not out.isOpened():
                print(f"错误: 无法创建输出视频文件 {output_path}")
                cap.release()
                return False

            # 处理每一帧
            frame_count = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # 裁剪帧（保留上半部分）
                cropped_frame = frame[:crop_height, :]
                out.write(cropped_frame)

                frame_count += 1
                if frame_count % 100 == 0:
                    progress = (frame_count / total_frames) * 100
                    print(f"  处理进度: {progress:.1f}% ({frame_count}/{total_frames})", end='\r')

            # 清理资源
            cap.release()
            out.release()

            print(f"  完成: {input_path.name} -> {output_path.name} ({width}x{height} -> {width}x{crop_height})")
            return True

        except Exception as e:
            print(f"错误: 处理视频 {input_path} 时发生异常: {e}")
            return False

    def process_all_videos(self, video_data: Dict[str, List[Tuple[str, str]]]) -> bool:
        """
        批量处理所有视频

        Args:
            video_data: 视频数据字典

        Returns:
            是否全部成功处理
        """
        print("开始批量处理视频...")

        total_videos = sum(len(videos) for videos in video_data.values())
        processed_count = 0
        failed_count = 0

        for category, videos in video_data.items():
            print(f"\n处理类别: {category}")

            for video_type, relative_path in videos:
                processed_count += 1

                # 构建输入和输出路径
                input_path = self.root_path / relative_path
                output_path = self.output_path / relative_path

                print(f"[{processed_count}/{total_videos}] 处理: {relative_path}")

                # 处理视频
                if self.crop_video(input_path, output_path):
                    pass  # 成功
                else:
                    failed_count += 1

        print(f"\n批量处理完成:")
        print(f"  总计: {total_videos} 个视频")
        print(f"  成功: {total_videos - failed_count} 个")
        print(f"  失败: {failed_count} 个")

        return failed_count == 0

    def verify_processed_videos(self, video_data: Dict[str, List[Tuple[str, str]]]) -> bool:
        """
        验证处理后的视频尺寸是否正确（高度应该是原始高度的一半）

        Args:
            video_data: 视频数据字典

        Returns:
            是否所有视频都符合预期尺寸
        """
        print("正在验证处理后的视频...")

        failed_videos = []
        total_videos = sum(len(videos) for videos in video_data.values())
        checked_count = 0

        for category, videos in video_data.items():
            for video_type, relative_path in videos:
                checked_count += 1

                # 构建输出路径
                output_path = self.output_path / relative_path

                if not output_path.exists():
                    failed_videos.append(f"{output_path} (文件不存在)")
                    continue

                # 检查视频尺寸（获取原始视频尺寸进行比较）
                try:
                    # 获取原始视频尺寸
                    original_path = self.root_path / relative_path
                    cap_orig = cv2.VideoCapture(str(original_path))
                    if cap_orig.isOpened():
                        orig_width = int(cap_orig.get(cv2.CAP_PROP_FRAME_WIDTH))
                        orig_height = int(cap_orig.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        cap_orig.release()
                        expected_height = orig_height // 2

                        # 检查处理后的视频尺寸
                        cap = cv2.VideoCapture(str(output_path))
                        if cap.isOpened():
                            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                            cap.release()

                            if width != orig_width or height != expected_height:
                                failed_videos.append(f"{output_path} (尺寸: {width}x{height}, 预期: {orig_width}x{expected_height})")
                        else:
                            failed_videos.append(f"{output_path} (无法打开)")
                    else:
                        failed_videos.append(f"{output_path} (无法获取原始视频尺寸)")
                except Exception as e:
                    failed_videos.append(f"{output_path} (检查异常: {e})")

        if failed_videos:
            print(f"发现 {len(failed_videos)} 个问题视频:")
            for failed_video in failed_videos[:10]:
                print(f"  - {failed_video}")
            if len(failed_videos) > 10:
                print(f"  ... 还有 {len(failed_videos) - 10} 个问题")
            return False
        else:
            print(f"验证完成: 所有 {total_videos} 个视频都符合预期尺寸（高度减半）")
            return True


def main():
    """主程序"""
    parser = argparse.ArgumentParser(description='BPPV数据预处理程序')
    parser.add_argument('--root', required=True,
                       help='BPPV数据根目录路径')
    parser.add_argument('--skip-processing', action='store_true',
                       help='跳过视频处理，仅生成路径文件')

    args = parser.parse_args()

    # 检查根目录是否存在
    if not os.path.exists(args.root):
        print(f"错误: 根目录不存在: {args.root}")
        sys.exit(1)

    # 初始化预处理器
    preprocessor = BPPVPreprocessor(args.root)

    print(f"BPPV数据预处理程序")
    print(f"输入目录: {preprocessor.root_path}")
    print(f"输出目录: {preprocessor.output_path}")
    print("-" * 50)

    try:
        # 1. 扫描视频文件
        video_data = preprocessor.scan_videos()
        total_videos = sum(len(videos) for videos in video_data.values())
        print(f"扫描完成: 发现 {total_videos} 个视频文件")

        if total_videos == 0:
            print("未发现任何视频文件，程序退出")
            sys.exit(1)

        # 2. 生成原始路径txt文件
        preprocessor.generate_txt_files(video_data, use_split_path=False)

        # 3. 验证原始文件路径
        if not preprocessor.verify_paths(video_data, use_split_path=False):
            print("警告: 部分原始文件路径无效")

        if not args.skip_processing:
            # 4. 创建输出目录结构
            preprocessor.create_output_structure(video_data)

            # 5. 批量处理视频
            if preprocessor.process_all_videos(video_data):
                print("所有视频处理成功")
            else:
                print("部分视频处理失败")

            # 6. 更新txt文件路径
            preprocessor.generate_txt_files(video_data, use_split_path=True)
            print("已更新txt文件路径指向处理后的视频")

            # 7. 验证处理后的视频
            if preprocessor.verify_processed_videos(video_data):
                print("所有处理后的视频验证通过")
            else:
                print("部分处理后的视频验证失败")

        print("-" * 50)
        print("预处理完成!")

    except KeyboardInterrupt:
        print("\n用户中断程序")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
